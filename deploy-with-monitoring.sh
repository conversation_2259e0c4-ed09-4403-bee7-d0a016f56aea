#!/bin/bash

# Script de Deploy com Monitoramento Automático de Memory Leaks
# Este script aplica todas as melhorias de prevenção de memory leaks

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKUP_DIR="$SCRIPT_DIR/backups/$(date +%Y%m%d_%H%M%S)"

echo "🚀 Iniciando deploy com monitoramento de memory leaks..."

# Função para log com timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Função para verificar se comando existe
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Verificar dependências
log "Verificando dependências..."
if ! command_exists docker; then
    echo "❌ Docker não está instalado"
    exit 1
fi

if ! command_exists docker-compose; then
    echo "❌ Docker Compose não está instalado"
    exit 1
fi

# Criar diretório de backup
mkdir -p "$BACKUP_DIR"
log "Diretório de backup criado: $BACKUP_DIR"

# Fazer backup do banco de dados se estiver rodando
if docker ps | grep -q postgres; then
    log "Fazendo backup do banco de dados..."
    docker exec sem-fronteiras-ssh-postgres-1 pg_dump -U postgres sem_fronteiras_ssh > "$BACKUP_DIR/database_backup.sql" || true
    log "Backup do banco salvo em: $BACKUP_DIR/database_backup.sql"
fi

# Fazer backup dos logs atuais
if docker ps | grep -q backend; then
    log "Fazendo backup dos logs..."
    docker logs sem-fronteiras-ssh-backend-1 > "$BACKUP_DIR/backend_logs.txt" 2>&1 || true
    log "Backup dos logs salvo em: $BACKUP_DIR/backend_logs.txt"
fi

# Parar serviços atuais
log "Parando serviços atuais..."
docker-compose down || true

# Limpar recursos do sistema
log "Limpando recursos do sistema..."
docker system prune -f || true

# Verificar se deve usar configuração de produção
USE_PRODUCTION=false
if [ "$1" = "--production" ] || [ "$NODE_ENV" = "production" ]; then
    USE_PRODUCTION=true
    log "Usando configuração de produção com monitoramento"
fi

# Construir e iniciar serviços
if [ "$USE_PRODUCTION" = true ]; then
    log "Iniciando com configuração de produção..."
    docker-compose -f docker-compose.yml -f docker-compose.production.yml up -d --build
else
    log "Iniciando com configuração de desenvolvimento..."
    docker-compose up -d --build
fi

# Aguardar serviços ficarem prontos
log "Aguardando serviços ficarem prontos..."
sleep 30

# Verificar saúde dos serviços
log "Verificando saúde dos serviços..."

# Verificar backend
if curl -f http://localhost:3000/health >/dev/null 2>&1; then
    log "✅ Backend está saudável"
else
    log "❌ Backend não está respondendo"
    docker logs sem-fronteiras-ssh-backend-1 --tail 20
fi

# Verificar frontend
if curl -f http://localhost:5173 >/dev/null 2>&1; then
    log "✅ Frontend está saudável"
else
    log "⚠️  Frontend pode não estar pronto ainda"
fi

# Verificar banco de dados
if docker exec sem-fronteiras-ssh-postgres-1 pg_isready -U postgres >/dev/null 2>&1; then
    log "✅ Banco de dados está saudável"
else
    log "❌ Banco de dados não está respondendo"
fi

# Verificar Redis
if docker exec sem-fronteiras-ssh-redis-1 redis-cli ping >/dev/null 2>&1; then
    log "✅ Redis está saudável"
else
    log "❌ Redis não está respondendo"
fi

# Configurar monitoramento se for produção
if [ "$USE_PRODUCTION" = true ]; then
    log "Configurando monitoramento automático..."
    
    # Tornar scripts executáveis
    chmod +x backend/scripts/*.sh
    
    # Configurar monitoramento como serviço (requer sudo)
    if [ "$EUID" -eq 0 ]; then
        log "Configurando serviço de monitoramento..."
        bash backend/scripts/setup-monitoring.sh
    else
        log "⚠️  Para configurar monitoramento automático, execute:"
        log "   sudo bash backend/scripts/setup-monitoring.sh"
    fi
fi

# Mostrar status final
log "📊 Status final dos serviços:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Mostrar informações úteis
echo ""
log "🎉 Deploy concluído com sucesso!"
echo ""
echo "📋 Informações úteis:"
echo "  - Frontend: http://localhost:5173"
echo "  - Backend: http://localhost:3000"
echo "  - Health Check: http://localhost:3000/health"
echo "  - Health Detailed: http://localhost:3000/health/detailed"
echo "  - Backup salvo em: $BACKUP_DIR"
echo ""
echo "🔍 Comandos de monitoramento:"
echo "  - Ver logs backend: docker logs -f sem-fronteiras-ssh-backend-1"
echo "  - Ver métricas: curl http://localhost:3000/health/detailed"
echo "  - Forçar limpeza: curl -X POST http://localhost:3000/health/cleanup"
echo ""

if [ "$USE_PRODUCTION" = true ]; then
    echo "🛡️  Monitoramento de produção ativo:"
    echo "  - Verificação automática a cada 5 minutos"
    echo "  - Limpeza automática de recursos"
    echo "  - Restart automático em situações críticas"
    echo "  - Logs em: /var/log/health-monitor.log"
    echo ""
fi

echo "⚠️  Para prevenir memory leaks:"
echo "  - Monitore uso de memória regularmente"
echo "  - Verifique logs de MaxListenersExceeded"
echo "  - Execute limpeza manual se necessário"
echo "  - Considere restart preventivo semanal"

# Verificar se há alertas imediatos
log "Verificando alertas imediatos..."
sleep 5

if curl -s http://localhost:3000/health/detailed | grep -q '"status":"warning"'; then
    echo ""
    echo "⚠️  ALERTA: Sistema iniciou com warnings"
    echo "   Verifique: curl http://localhost:3000/health/detailed"
fi

if curl -s http://localhost:3000/health/detailed | grep -q '"status":"critical"'; then
    echo ""
    echo "🚨 CRÍTICO: Sistema iniciou com problemas críticos"
    echo "   Verifique: curl http://localhost:3000/health/detailed"
fi

log "Deploy finalizado!"

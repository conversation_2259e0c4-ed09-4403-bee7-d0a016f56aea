#!/bin/bash

# Script para configurar o monitoramento automático de saúde
# Este script instala e configura o health-monitor como um serviço systemd

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVICE_NAME="sem-fronteiras-health-monitor"
SERVICE_FILE="/etc/systemd/system/${SERVICE_NAME}.service"

echo "🔧 Configurando monitoramento automático de saúde..."

# Verificar se está rodando como root
if [ "$EUID" -ne 0 ]; then
    echo "❌ Este script deve ser executado como root (use sudo)"
    exit 1
fi

# Tornar o script executável
chmod +x "$SCRIPT_DIR/health-monitor.sh"

# Criar arquivo de serviço systemd
cat > "$SERVICE_FILE" << EOF
[Unit]
Description=Sem Fronteiras Health Monitor
After=docker.service
Requires=docker.service

[Service]
Type=simple
User=root
ExecStart=$SCRIPT_DIR/health-monitor.sh
Restart=always
RestartSec=30
StandardOutput=journal
StandardError=journal

# Configurações de segurança
NoNewPrivileges=true
PrivateTmp=true

[Install]
WantedBy=multi-user.target
EOF

echo "✅ Arquivo de serviço criado: $SERVICE_FILE"

# Recarregar systemd
systemctl daemon-reload

# Habilitar o serviço
systemctl enable "$SERVICE_NAME"

echo "✅ Serviço habilitado para iniciar automaticamente"

# Iniciar o serviço
systemctl start "$SERVICE_NAME"

# Verificar status
if systemctl is-active --quiet "$SERVICE_NAME"; then
    echo "✅ Serviço iniciado com sucesso"
    echo ""
    echo "📋 Comandos úteis:"
    echo "  - Ver status: sudo systemctl status $SERVICE_NAME"
    echo "  - Ver logs: sudo journalctl -u $SERVICE_NAME -f"
    echo "  - Parar: sudo systemctl stop $SERVICE_NAME"
    echo "  - Reiniciar: sudo systemctl restart $SERVICE_NAME"
    echo "  - Desabilitar: sudo systemctl disable $SERVICE_NAME"
    echo ""
    echo "📁 Logs detalhados em: /var/log/health-monitor.log"
else
    echo "❌ Falha ao iniciar o serviço"
    echo "Verificar logs: sudo journalctl -u $SERVICE_NAME"
    exit 1
fi

echo ""
echo "🎉 Monitoramento automático configurado com sucesso!"
echo "O sistema agora monitora automaticamente:"
echo "  - Uso de memória (alerta > 85%)"
echo "  - Memory leaks (MaxListenersExceeded)"
echo "  - Conexões SSH excessivas (> 20)"
echo "  - Processos Node.js órfãos"
echo "  - Limpeza automática de recursos"
echo "  - Restart automático em situações críticas"

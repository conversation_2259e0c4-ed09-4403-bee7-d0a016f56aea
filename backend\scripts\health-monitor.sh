#!/bin/bash

# Script de Monitoramento Contínuo de Saúde do Sistema
# Este script monitora memory leaks, uso de recursos e saúde geral do container
# Executa verificações a cada 5 minutos e toma ações corretivas quando necessário

CONTAINER_NAME="sem-fronteiras-ssh-backend-1"
LOG_FILE="/var/log/health-monitor.log"
MAX_MEMORY_USAGE=85
MAX_SSH_CONNECTIONS=20
MAX_NODE_PROCESSES=3

# Função para log com timestamp
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Função para verificar se o container está rodando
check_container_running() {
    if ! docker ps | grep -q "$CONTAINER_NAME"; then
        log_message "❌ ERRO: Container $CONTAINER_NAME não está rodando"
        return 1
    fi
    return 0
}

# Função para verificar uso de memória
check_memory_usage() {
    local memory_usage
    memory_usage=$(docker stats --no-stream --format "{{.MemPerc}}" "$CONTAINER_NAME" 2>/dev/null | sed 's/%//')
    
    if [ -z "$memory_usage" ]; then
        log_message "⚠️  Não foi possível obter uso de memória"
        return 1
    fi
    
    log_message "📊 Uso de memória: ${memory_usage}%"
    
    if [ "${memory_usage%.*}" -gt $MAX_MEMORY_USAGE ]; then
        log_message "🚨 ALERTA: Uso de memória alto (${memory_usage}%)"
        return 1
    fi
    
    return 0
}

# Função para verificar memory leaks nos logs
check_memory_leaks() {
    local leak_count
    leak_count=$(docker logs --since 10m "$CONTAINER_NAME" 2>/dev/null | grep -c "MaxListenersExceeded" || echo "0")
    
    if [ "$leak_count" -gt 0 ]; then
        log_message "🚨 MEMORY LEAK DETECTADO: $leak_count ocorrências nos últimos 10 minutos"
        return 1
    fi
    
    return 0
}

# Função para verificar conexões SSH excessivas
check_ssh_connections() {
    local ssh_count
    ssh_count=$(docker exec "$CONTAINER_NAME" ps aux 2>/dev/null | grep ssh | grep -v grep | wc -l || echo "0")
    
    log_message "🔗 Conexões SSH ativas: $ssh_count"
    
    if [ "$ssh_count" -gt $MAX_SSH_CONNECTIONS ]; then
        log_message "⚠️  Muitas conexões SSH ativas ($ssh_count)"
        return 1
    fi
    
    return 0
}

# Função para verificar processos Node.js excessivos
check_node_processes() {
    local node_count
    node_count=$(docker exec "$CONTAINER_NAME" ps aux 2>/dev/null | grep node | grep -v grep | wc -l || echo "0")
    
    log_message "⚙️  Processos Node.js: $node_count"
    
    if [ "$node_count" -gt $MAX_NODE_PROCESSES ]; then
        log_message "⚠️  Muitos processos Node.js ($node_count)"
        return 1
    fi
    
    return 0
}

# Função para limpeza preventiva
cleanup_resources() {
    log_message "🧹 Executando limpeza preventiva de recursos..."
    
    # Limpar processos SSH órfãos
    docker exec "$CONTAINER_NAME" bash -c "ps aux | grep ssh | grep -v grep | awk '{print \$2}' | xargs -r kill -9" 2>/dev/null || true
    
    # Limpar arquivos temporários antigos
    docker exec "$CONTAINER_NAME" bash -c "find /tmp -name 'ssh_*' -mmin +30 -delete" 2>/dev/null || true
    docker exec "$CONTAINER_NAME" bash -c "find /tmp -name '.ssh_*' -mmin +30 -delete" 2>/dev/null || true
    docker exec "$CONTAINER_NAME" bash -c "find /tmp -name '*.lock' -mmin +60 -delete" 2>/dev/null || true
    
    # Forçar garbage collection no Node.js
    docker exec "$CONTAINER_NAME" bash -c "kill -USR2 \$(pgrep node)" 2>/dev/null || true
    
    log_message "✅ Limpeza preventiva concluída"
}

# Função para restart de emergência
emergency_restart() {
    log_message "🚨 EXECUTANDO RESTART DE EMERGÊNCIA"
    
    # Tentar limpeza primeiro
    cleanup_resources
    sleep 10
    
    # Se ainda houver problemas, reiniciar container
    if ! check_memory_usage || ! check_memory_leaks; then
        log_message "🔄 Reiniciando container $CONTAINER_NAME..."
        docker restart "$CONTAINER_NAME"
        sleep 30
        
        if check_container_running; then
            log_message "✅ Container reiniciado com sucesso"
        else
            log_message "❌ ERRO: Falha ao reiniciar container"
        fi
    fi
}

# Função principal de monitoramento
monitor_health() {
    log_message "🔍 Iniciando verificação de saúde..."
    
    # Verificar se container está rodando
    if ! check_container_running; then
        return 1
    fi
    
    local issues=0
    
    # Verificar uso de memória
    if ! check_memory_usage; then
        issues=$((issues + 1))
    fi
    
    # Verificar memory leaks
    if ! check_memory_leaks; then
        issues=$((issues + 2))  # Memory leaks são mais graves
    fi
    
    # Verificar conexões SSH
    if ! check_ssh_connections; then
        issues=$((issues + 1))
    fi
    
    # Verificar processos Node.js
    if ! check_node_processes; then
        issues=$((issues + 1))
    fi
    
    # Tomar ações baseadas na gravidade
    if [ $issues -ge 3 ]; then
        log_message "🚨 SITUAÇÃO CRÍTICA: $issues problemas detectados"
        emergency_restart
    elif [ $issues -ge 1 ]; then
        log_message "⚠️  Problemas detectados: $issues - executando limpeza preventiva"
        cleanup_resources
    else
        log_message "✅ Sistema saudável"
    fi
    
    log_message "📋 Verificação concluída (problemas: $issues)"
    echo "---"
}

# Função principal
main() {
    log_message "🚀 Iniciando Health Monitor para $CONTAINER_NAME"
    
    # Criar diretório de log se não existir
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # Loop principal
    while true; do
        monitor_health
        sleep 300  # 5 minutos
    done
}

# Verificar se está sendo executado como script principal
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    # Verificar se já está rodando
    if pgrep -f "health-monitor.sh" > /dev/null; then
        echo "Health monitor já está rodando"
        exit 1
    fi
    
    # Executar função principal
    main
fi

version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./backend/src:/app/src
      - ./backend/prisma:/app/prisma
      - ./backend/package.json:/app/package.json
      - ./backend/tsconfig.json:/app/tsconfig.json
      - ./update.sh:/app/update.sh
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/sem_fronteiras
      - REDIS_URL=redis://redis:6379
      - WATCHPACK_POLLING=true
      - JWT_SECRET=sua_chave_secreta_muito_segura_aqui
      - SESSION_SECRET=outra_chave_secreta_muito_segura_aqui
    command: npm run dev
    depends_on:
      - postgres
      - redis
    # Limitar recursos para evitar consumo excessivo
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 4G        # Aumentado de 2G para 4G (servidor dedicado)
        reservations:
          cpus: '0.5'
          memory: 1G
    # Configurar política de reinicialização
    restart: unless-stopped
    # Verificar saúde do container
    healthcheck:
      test: ["CMD", "wget", "-q", "--spider", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "5173:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - WATCHPACK_POLLING=true
      - CHOKIDAR_USEPOLLING=true
    command: npm run dev -- --host
    depends_on:
      - backend

  postgres:
    image: postgres:16-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=sem_fronteiras
    volumes:
      - postgres_data:/var/lib/postgresql/data
    # Otimizado para servidor dedicado
    deploy:
      resources:
        limits:
          memory: 1G        # Limite generoso para PostgreSQL
        reservations:
          memory: 256M

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    # Otimizado para servidor dedicado
    deploy:
      resources:
        limits:
          memory: 512M      # Limite generoso para Redis
        reservations:
          memory: 128M

  # pg_backup:
  #   image: alpine
  #   container_name: pg_backup
  #   restart: always
  #   depends_on:
  #     - postgres
  #   environment:
  #     - PGPASSWORD=postgres
  #   volumes:
  #     - postgres_backups:/backups
  #     - ./backups-externos:/backups-externos
  #   command: >
  #     sh -c "
  #       apk add --no-cache postgresql-client &&
  #       mkdir -p /backups &&
  #       mkdir -p /backups-externos &&
  #       while true; do
  #         TIMESTAMP=\$$(date +%Y%m%d_%H%M%S) &&
  #         BACKUP_FILE=/backups/backup_\$$TIMESTAMP.sql &&
  #         BACKUP_EXTERNO=/backups-externos/backup_\$$TIMESTAMP.sql &&
  #
  #         # Criar backup
  #         pg_dump -h postgres -U postgres sem_fronteiras > \$$BACKUP_FILE &&
  #
  #         # Copiar para diretório externo
  #         cp \$$BACKUP_FILE \$$BACKUP_EXTERNO &&
  #
  #         # Limpar backups antigos (manter últimos 30 dias)
  #         find /backups -type f -mtime +30 -delete &&
  #         find /backups-externos -type f -mtime +30 -delete &&
  #
  #         # Aguardar 24 horas
  #         sleep 86400
  #       done
  #     "

  # NOTA: Container pg_backup desabilitado para evitar redundância.
  # O backup é realizado via script cron (backup-externo.sh) diariamente à meia-noite.

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  # postgres_backups:
  #   driver: local
  #   name: sem-fronteiras-backups

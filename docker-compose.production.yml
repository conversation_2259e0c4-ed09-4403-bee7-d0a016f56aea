# Docker Compose para Produção com Monitoramento de Memory Leaks
# Este arquivo inclui configurações otimizadas para prevenir memory leaks

version: '3.8'

services:
  postgres:
    image: postgres:16-alpine
    container_name: sem-fronteiras-ssh-postgres-1
    environment:
      POSTGRES_DB: sem_fronteiras_ssh
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    container_name: sem-fronteiras-ssh-redis-1
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  backend:
    build: ./backend
    container_name: sem-fronteiras-ssh-backend-1
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=***********************************************/sem_fronteiras_ssh
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key-here
      - NODE_ENV=production
      # Configurações para prevenir memory leaks
      - NODE_OPTIONS=--max-old-space-size=1536 --max-semi-space-size=128 --expose-gc
      - UV_THREADPOOL_SIZE=4
      - NODE_MAX_LISTENERS=20
    volumes:
      - ./backend/src:/app/src
      - ./backend/prisma:/app/prisma
      - ./backend/package.json:/app/package.json
      - ./backend/tsconfig.json:/app/tsconfig.json
      # Volume para logs de monitoramento
      - monitoring_logs:/var/log
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
    # Configurações de sistema para prevenir memory leaks
    ulimits:
      nproc: 100      # Limitar número de processos
      nofile: 1024    # Limitar arquivos abertos
      memlock: 67108864  # Limitar memória bloqueada
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  frontend:
    build: ./frontend
    container_name: sem-fronteiras-ssh-frontend-1
    ports:
      - "5173:5173"
    volumes:
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
      - ./frontend/index.html:/app/index.html
      - ./frontend/vite.config.ts:/app/vite.config.ts
      - ./frontend/tailwind.config.js:/app/tailwind.config.js
      - ./frontend/postcss.config.js:/app/postcss.config.js
      - ./frontend/package.json:/app/package.json
      - ./frontend/tsconfig.json:/app/tsconfig.json
    depends_on:
      - backend
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
    restart: unless-stopped

  # Serviço de backup do banco de dados
  pg_backup:
    image: alpine
    container_name: pg_backup
    volumes:
      - postgres_data:/var/lib/postgresql/data:ro
      - ./backups:/backups
    command: >
      sh -c "
        apk add --no-cache postgresql-client &&
        while true; do
          sleep 86400
        done
      "
    depends_on:
      - postgres
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.1'
    restart: unless-stopped

  # Serviço de monitoramento de saúde (opcional)
  health-monitor:
    image: alpine
    container_name: health-monitor
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./backend/scripts:/scripts:ro
      - monitoring_logs:/var/log
    command: >
      sh -c "
        apk add --no-cache docker-cli bash curl &&
        chmod +x /scripts/health-monitor.sh &&
        /scripts/health-monitor.sh
      "
    depends_on:
      - backend
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.1'
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  monitoring_logs:
    driver: local

networks:
  default:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

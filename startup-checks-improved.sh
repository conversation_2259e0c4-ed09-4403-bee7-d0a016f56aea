#!/bin/sh

# Script de verificação de inicialização para o servidor Node.js
# Este script é executado quando o container Docker é iniciado

echo "Executando verificações de inicialização..."

# Verificar se o diretório de logs existe
if [ ! -d "/app/logs" ]; then
  echo "Criando diretório de logs..."
  mkdir -p /app/logs
  chmod 755 /app/logs
fi

# Verificar configurações do Node.js
echo "Verificando configurações do Node.js..."
node -e "console.log('Versão do Node.js:', process.version)"
node -e "console.log('Memória máxima:', require('v8').getHeapStatistics().heap_size_limit / (1024 * 1024), 'MB')"

# NOVA FUNCIONALIDADE: Verificar e instalar dependências automaticamente
echo "Verificando dependências do Node.js..."

# Verificar se package.json foi modificado ou se node_modules está incompleto
PACKAGE_CHANGED=false
DEPENDENCIES_MISSING=false

# Verificar se alguma dependência crítica está faltando
if ! node -e "require('telnet-client')" 2>/dev/null; then
  echo "AVISO: Dependência 'telnet-client' não encontrada"
  DEPENDENCIES_MISSING=true
fi

if ! node -e "require('node-ssh')" 2>/dev/null; then
  echo "AVISO: Dependência 'node-ssh' não encontrada"
  DEPENDENCIES_MISSING=true
fi

if ! node -e "require('node-routeros-v2')" 2>/dev/null; then
  echo "AVISO: Dependência 'node-routeros-v2' não encontrada"
  DEPENDENCIES_MISSING=true
fi

# Se dependências estão faltando, instalar automaticamente
if [ "$DEPENDENCIES_MISSING" = true ]; then
  echo "Instalando dependências faltantes automaticamente..."
  npm install --production=false
  
  if [ $? -eq 0 ]; then
    echo "Dependências instaladas com sucesso!"
  else
    echo "ERRO: Falha ao instalar dependências"
    exit 1
  fi
else
  echo "Todas as dependências estão disponíveis"
fi

# Verificar conexão com o banco de dados
echo "Verificando conexão com o banco de dados..."

# Aguardar o banco de dados estar pronto
echo "Aguardando o banco de dados estar pronto..."
sleep 5

# Usar npx prisma migrate para verificar a conexão
npx prisma migrate deploy

if [ $? -eq 0 ]; then
  echo "Conexão com o banco de dados OK"
else
  echo "AVISO: Não foi possível conectar ao banco de dados, mas continuando a inicialização"
  # Não sair com erro para permitir que o container inicie mesmo sem banco de dados
fi

# Verificar conexão com o Redis
echo "Verificando conexão com o Redis..."
if command -v redis-cli >/dev/null 2>&1; then
  redis-cli -u $REDIS_URL PING
  if [ $? -eq 0 ]; then
    echo "Conexão com o Redis OK"
  else
    echo "ERRO: Não foi possível conectar ao Redis"
  fi
else
  echo "redis-cli não disponível, pulando verificação do Redis"
fi

# Verificar se há memória suficiente
FREE_MEM=$(node -e "console.log(Math.floor(require('os').freemem() / (1024 * 1024)))")
echo "Memória livre: $FREE_MEM MB"

# Verificar se há espaço em disco suficiente
DISK_SPACE=$(df -h / | awk 'NR==2 {print $4}')
echo "Espaço em disco disponível: $DISK_SPACE"

# Verificar se há arquivos de log muito grandes
echo "Verificando arquivos de log..."
find /app/logs -type f -name "*.log" -size +100M | while read log_file; do
  echo "AVISO: Arquivo de log grande encontrado: $log_file"
  echo "Rotacionando arquivo de log..."
  mv "$log_file" "${log_file}.$(date +%Y%m%d%H%M%S)"
  touch "$log_file"
done

# Verificar se há processos zumbis
ZOMBIE_COUNT=$(ps aux | grep -w Z | wc -l)
if [ $ZOMBIE_COUNT -gt 0 ]; then
  echo "AVISO: $ZOMBIE_COUNT processos zumbis encontrados"
fi

# Verificar se há sessões SSH pendentes
echo "Verificando sessões SSH pendentes..."
SSH_COUNT=$(ps aux | grep ssh | grep -v grep | wc -l)
if [ $SSH_COUNT -gt 0 ]; then
  echo "AVISO: $SSH_COUNT sessões SSH pendentes encontradas"
  echo "Executando limpeza de sessões SSH..."

  # Matar processos SSH pendentes
  ps aux | grep ssh | grep -v grep | awk '{print $2}' | xargs -r kill -9

  echo "Limpeza de sessões SSH concluída"
fi

echo "Verificações de inicialização concluídas"

# Verificar e limpar arquivos de bloqueio SSH
echo "Verificando arquivos de bloqueio SSH..."
find /tmp -name "ssh_*" -type f -mmin +60 -delete 2>/dev/null
find /tmp -name ".ssh_*" -type f -mmin +60 -delete 2>/dev/null

# Verificar integridade do banco de dados
echo "Verificando integridade do banco de dados..."
USER_COUNT=$(node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
prisma.user.count().then(count => {
  console.log(count);
  prisma.\$disconnect();
}).catch(() => {
  console.log(0);
  prisma.\$disconnect();
});
" 2>/dev/null || echo "0")

SERVER_COUNT=$(node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
prisma.server.count().then(count => {
  console.log(count);
  prisma.\$disconnect();
}).catch(() => {
  console.log(0);
  prisma.\$disconnect();
});
" 2>/dev/null || echo "0")

COMMAND_COUNT=$(node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
prisma.command.count().then(count => {
  console.log(count);
  prisma.\$disconnect();
}).catch(() => {
  console.log(0);
  prisma.\$disconnect();
});
" 2>/dev/null || echo "0")

echo "Usuários encontrados: $USER_COUNT"
echo "Servidores encontrados: $SERVER_COUNT"
echo "Comandos encontrados: $COMMAND_COUNT"
echo "Verificação de integridade concluída!"

# Verificar se existe pelo menos um administrador ativo
ADMIN_COUNT=$(node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
prisma.user.count({
  where: {
    role: 'ADMIN',
    isActive: true
  }
}).then(count => {
  console.log(count);
  prisma.\$disconnect();
}).catch(() => {
  console.log(0);
  prisma.\$disconnect();
});
" 2>/dev/null || echo "0")

echo "Verificando se existe pelo menos um administrador ativo..."
echo "$ADMIN_COUNT administrador(es) ativo(s) encontrado(s)."

# Iniciar o servidor Node.js
echo "🚀 HTTP server running!"
exec "$@"
